import { Router } from '@angular/router';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { DeviceAccessService } from '../../services/device-access.service';

describe('canLoadFeatureByDevice', () => {
  let spectator: SpectatorService<DeviceAccessService>;

  const createService = createServiceFactory({
    service: DeviceAccessService,
    mocks: [Router],
  });

  beforeEach(() => {
    spectator = createService();
  });
});
