import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { UnavailableDeviceComponent } from './unavailable-device.component';

describe('UnavailableDeviceComponent', () => {
  let spectator: Spectator<UnavailableDeviceComponent>;
  const createComponent = createComponentFactory({
    component: UnavailableDeviceComponent,
    declarations: [MockDirective(TranslocoDirective)],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });
});
