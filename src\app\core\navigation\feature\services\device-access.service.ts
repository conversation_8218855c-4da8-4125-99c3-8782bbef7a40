import { inject, Injectable } from '@angular/core';
import { DeviceDetectorService } from 'ngx-device-detector';
import { DeviceType } from '../../models/types/device.type';
// TODO rename file
@Injectable({
  providedIn: 'root',
})
export class DeviceAccessService {
  private readonly deviceService = inject(DeviceDetectorService);
  private readonly deviceType: DeviceType;

  constructor() {
    this.deviceType = this.deviceService.getDeviceInfo()
      .deviceType as DeviceType;
  }

  allowedFor(deviceTypes?: DeviceType[]): boolean {
    if (!deviceTypes?.length) {
      return true;
    }
    return deviceTypes?.includes(this.deviceType);
  }
}
