import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { DeviceAccessService } from './device-access.service';

describe('DeviceAccessService', () => {
  let spectator: SpectatorService<DeviceAccessService>;
  const createService = createServiceFactory(DeviceAccessService);

  beforeEach(() => {
    spectator = createService();
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });
});
