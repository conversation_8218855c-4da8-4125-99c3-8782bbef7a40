import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { DeviceDetectorService } from 'ngx-device-detector';
import { DeviceAccessService } from './device-access.service';
import { DeviceType } from '../../models/types/device.type';

describe('DeviceAccessService', () => {
  let spectator: SpectatorService<DeviceAccessService>;
  let deviceDetectorService: jest.Mocked<DeviceDetectorService>;

  const createService = createServiceFactory({
    service: DeviceAccessService,
    mocks: [DeviceDetectorService],
  });

  beforeEach(() => {
    spectator = createService();
    deviceDetectorService = spectator.inject(DeviceDetectorService);
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('constructor', () => {
    it('should initialize deviceType from DeviceDetectorService', () => {
      const mockDeviceInfo = { deviceType: 'mobile' };
      deviceDetectorService.getDeviceInfo.mockReturnValue(
        mockDeviceInfo as any
      );

      const newSpectator = createService();

      expect(deviceDetectorService.getDeviceInfo).toHaveBeenCalled();
      expect(newSpectator.service).toBeTruthy();
    });

    it('should handle different device types', () => {
      const deviceTypes: DeviceType[] = [
        'mobile',
        'tablet',
        'desktop',
        'unknown',
      ];

      deviceTypes.forEach((deviceType) => {
        const mockDeviceInfo = { deviceType };
        deviceDetectorService.getDeviceInfo.mockReturnValue(
          mockDeviceInfo as any
        );

        const newSpectator = createService();
        expect(newSpectator.service).toBeTruthy();
      });
    });
  });

  describe('allowedFor', () => {
    beforeEach(() => {
      const mockDeviceInfo = { deviceType: 'mobile' };
      jest
        .spyOn(deviceDetectorService, 'getDeviceInfo')
        .mockReturnValue(mockDeviceInfo as any);
    });

    it('should return true when no deviceTypes are provided', () => {
      const result = spectator.service.allowedFor();
      expect(result).toBe(true);
    });

    it('should return true when empty array is provided', () => {
      const result = spectator.service.allowedFor([]);
      expect(result).toBe(true);
    });

    it('should return true when current device is in allowed list', () => {
      // Device is mobile, and mobile is in the allowed list
      const result = spectator.service.allowedFor(['mobile', 'tablet']);
      expect(result).toBe(true);
    });

    it('should return false when current device is not in allowed list', () => {
      // Device is mobile, but only tablet and desktop are allowed
      const result = spectator.service.allowedFor(['tablet', 'desktop']);
      expect(result).toBe(false);
    });

    it('should work correctly for tablet device', () => {
      // Mock device as tablet
      const mockDeviceInfo = { deviceType: 'tablet' };
      deviceDetectorService.getDeviceInfo.mockReturnValue(
        mockDeviceInfo as any
      );

      const newSpectator = createService();

      expect(newSpectator.service.allowedFor(['tablet'])).toBe(true);
      expect(newSpectator.service.allowedFor(['mobile'])).toBe(false);
      expect(newSpectator.service.allowedFor(['tablet', 'desktop'])).toBe(true);
    });

    it('should work correctly for desktop device', () => {
      // Mock device as desktop
      const mockDeviceInfo = { deviceType: 'desktop' };
      deviceDetectorService.getDeviceInfo.mockReturnValue(
        mockDeviceInfo as any
      );

      const newSpectator = createService();

      expect(newSpectator.service.allowedFor(['desktop'])).toBe(true);
      expect(newSpectator.service.allowedFor(['mobile', 'tablet'])).toBe(false);
      expect(
        newSpectator.service.allowedFor(['mobile', 'tablet', 'desktop'])
      ).toBe(true);
    });

    it('should work correctly for unknown device', () => {
      // Mock device as unknown
      const mockDeviceInfo = { deviceType: 'unknown' };
      deviceDetectorService.getDeviceInfo.mockReturnValue(
        mockDeviceInfo as any
      );

      const newSpectator = createService();

      expect(newSpectator.service.allowedFor(['unknown'])).toBe(true);
      expect(
        newSpectator.service.allowedFor(['mobile', 'tablet', 'desktop'])
      ).toBe(false);
      expect(
        newSpectator.service.allowedFor([
          'mobile',
          'tablet',
          'desktop',
          'unknown',
        ])
      ).toBe(true);
    });

    it('should handle single device type in array', () => {
      const result = spectator.service.allowedFor(['mobile']);
      expect(result).toBe(true);
    });

    it('should handle multiple device types in array', () => {
      const result = spectator.service.allowedFor([
        'mobile',
        'tablet',
        'desktop',
      ]);
      expect(result).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle null deviceTypes', () => {
      const result = spectator.service.allowedFor(null as any);
      expect(result).toBe(true);
    });

    it('should handle undefined deviceTypes', () => {
      const result = spectator.service.allowedFor();
      expect(result).toBe(true);
    });

    it('should handle when DeviceDetectorService returns unexpected deviceType', () => {
      const mockDeviceInfo = { deviceType: 'smartwatch' }; // Not in DeviceType union
      deviceDetectorService.getDeviceInfo.mockReturnValue(
        mockDeviceInfo as any
      );

      const newSpectator = createService();

      // Should still work, but won't match any of our known types
      expect(
        newSpectator.service.allowedFor(['mobile', 'tablet', 'desktop'])
      ).toBe(false);
      expect(newSpectator.service.allowedFor(['smartwatch'] as any)).toBe(true);
    });
  });
});
